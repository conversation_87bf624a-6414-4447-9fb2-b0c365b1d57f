{"geocoded_waypoints": [{"geocoder_status": "OK", "place_id": "ChIJI0YGGSptdkgRfyYEw933A1A", "types": ["establishment", "moving_company", "point_of_interest", "storage"]}, {"geocoder_status": "OK", "place_id": "ChIJn6ugNJsPdkgRutTlZLRu2vo", "types": ["street_address"]}, {"geocoder_status": "OK", "place_id": "ChIJlYCnpWkPdkgRvyey_CVsFsI", "types": ["beauty_salon", "establishment", "point_of_interest"]}, {"geocoder_status": "OK", "place_id": "ChIJH-cnq50PdkgRBmjWBK-1NK8", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJb5U8Zp4PdkgRl13VQGAiUrI", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJvRXrV4QPdkgRZ4hI5oaf1vc", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJ3RhC1oEPdkgRvD7cm_31TIg", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJe271nIIPdkgR4fIGQkjElCM", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJXT7c93wFdkgRYyAHbJySW4g", "types": ["street_address"]}, {"geocoder_status": "OK", "place_id": "ChIJWbYN5UwFdkgReo3hyKs6b_c", "types": ["establishment", "point_of_interest"]}, {"geocoder_status": "OK", "place_id": "ChIJBTTxxXoFdkgRjfMspaVDyTM", "types": ["street_address"]}, {"geocoder_status": "OK", "place_id": "ChIJq_LHCMAFdkgRKnEv7lrUZrU", "types": ["doctor", "establishment", "health", "point_of_interest"]}, {"geocoder_status": "OK", "place_id": "ChIJhUzh62cFdkgRMf5qbMOorTE", "types": ["street_address"]}, {"geocoder_status": "OK", "place_id": "ChIJhZAKIGgFdkgRPOomnp8XvRI", "types": ["establishment", "food", "point_of_interest", "restaurant"]}, {"geocoder_status": "OK", "place_id": "ChIJNWuOnEIFdkgRA3u9lrYraCI", "types": ["street_address"]}, {"geocoder_status": "OK", "place_id": "ChIJ855mAWkFdkgRYqEcJTyG9qY", "types": ["establishment", "health", "point_of_interest"]}, {"geocoder_status": "OK", "place_id": "ChIJf11tP4wFdkgRNNaLF9cTgcg", "types": ["establishment", "point_of_interest"]}, {"geocoder_status": "OK", "place_id": "ChIJU23B9W4FdkgRzCwt5cuElyc", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJoaWusW4FdkgRQPr01wuKZDI", "types": ["premise"]}, {"geocoder_status": "OK", "place_id": "ChIJtSG0CWwFdkgRyx1c8piacaQ", "types": ["premise"]}], "routes": [{"bounds": {"northeast": {"lat": 51.5577478, "lng": -0.1694345}, "southwest": {"lat": 51.4731469, "lng": -0.4099612}}, "copyrights": "Map data ©2024 Google", "legs": [{"distance": {"text": "25.9 km", "value": 25929}, "duration": {"text": "41 mins", "value": 2443}, "end_address": "21 Fulham Rd., London SW6 5NR, UK", "end_location": {"lat": 51.4802373, "lng": -0.1989233}, "start_address": "19 Stonefield Way, Ruislip HA4 0BJ, UK", "start_location": {"lat": 51.555654, "lng": -0.383046}, "steps": [{"distance": {"text": "69 m", "value": 69}, "duration": {"text": "1 min", "value": 11}, "end_location": {"lat": 51.5559249, "lng": -0.3821529}, "html_instructions": "Head <b>northeast</b>", "polyline": {"points": "ymtyH`yiAa@yBSy@"}, "start_location": {"lat": 51.555654, "lng": -0.383046}, "travel_mode": "DRIVING"}, {"distance": {"text": "15 m", "value": 15}, "duration": {"text": "1 min", "value": 8}, "end_location": {"lat": 51.556041, "lng": -0.3822669}, "html_instructions": "Turn <b>left</b> toward <b>Stonefield Way</b>", "maneuver": "turn-left", "polyline": {"points": "ootyHlsiAWV"}, "start_location": {"lat": 51.5559249, "lng": -0.3821529}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.5 km", "value": 467}, "duration": {"text": "2 mins", "value": 112}, "end_location": {"lat": 51.5577478, "lng": -0.3863925}, "html_instructions": "Turn <b>left</b> onto <b>Stonefield Way</b>", "maneuver": "turn-left", "polyline": {"points": "gptyHdtiA@Fr@rDDPF\\H^Lr@FV@L@B@BFZBNF\\BZEF_@X_BdA_@VCBSLQLgAt@a@Xa@VYRk@^YR[R"}, "start_location": {"lat": 51.556041, "lng": -0.3822669}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 114}, "duration": {"text": "1 min", "value": 14}, "end_location": {"lat": 51.55724530000001, "lng": -0.3878238}, "html_instructions": "Turn <b>left</b> onto <b>Victoria Rd</b>", "maneuver": "turn-left", "polyline": {"points": "}ztyH|mjAZ~ANr@Nn@Lf@JZ@B@B@BBBBD"}, "start_location": {"lat": 51.5577478, "lng": -0.3863925}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.7 km", "value": 653}, "duration": {"text": "1 min", "value": 83}, "end_location": {"lat": 51.5575623, "lng": -0.3957973}, "html_instructions": "At the roundabout, take the <b>2nd</b> exit and stay on <b>Victoria Rd</b><div style=\"font-size:0.9em\">Go through 1 roundabout</div>", "maneuver": "roundabout-left", "polyline": {"points": "ywtyHzvjA@??A@A?A@??A@??A@?@?@A@?@@@?@??@@??@@??@@??@?@@??@?@?@@??@?@?@?@?@@@?@?@?@?@A@?@?@?@?@?@A??@?@A@?@A??@H|@VrA\\lANl@FLFHBHFTPj@DRBPJn@F`@Db@Fd@B`@@PB^Bl@?J?T@\\?`B@l@?@@??@?@?@@??@?@?@?@A@?@?@A??@?@A?A@A?OtAEFADEXIb@G^AJKf@K^I^Qj@ITITOZEFCHCDUd@INMRKPKNORUZIJ"}, "start_location": {"lat": 51.55724530000001, "lng": -0.3878238}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 238}, "duration": {"text": "1 min", "value": 47}, "end_location": {"lat": 51.55642719999999, "lng": -0.3985648}, "html_instructions": "Turn <b>left</b> onto <b>Long Dr</b>", "maneuver": "turn-left", "polyline": {"points": "wytyHvhlACnBDHNb@@BHVN^JXBFFLHVDJBFLTZp@h@jAr@|A"}, "start_location": {"lat": 51.5575623, "lng": -0.3957973}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.5 km", "value": 464}, "duration": {"text": "1 min", "value": 67}, "end_location": {"lat": 51.5539946, "lng": -0.4039911}, "html_instructions": "Continue onto <b>Station Approach</b>", "polyline": {"points": "urtyH~ylAH<PERSON>BBJPj@nANXj@jAJVJZPd@DNX~@JXHX`@pAPj@FTDLPl@f@~AFRBHFRr@|BX~@ZhAJX"}, "start_location": {"lat": 51.55642719999999, "lng": -0.3985648}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.7 km", "value": 683}, "duration": {"text": "1 min", "value": 84}, "end_location": {"lat": 51.5486212, "lng": -0.3993082}, "html_instructions": "Turn <b>left</b> onto <b>W End Rd</b>/<wbr/><b>A4180</b>", "maneuver": "turn-left", "polyline": {"points": "mctyH|{mAZSHCH?VSPOfA}@`@[JKh@c@JIfCuBz@u@h@g@p@m@v@s@ZW@CROFELMJIDEb@[z@{@FEbAw@PO`@Y@?|@m@@?BAFEPKXONa@N]"}, "start_location": {"lat": 51.5539946, "lng": -0.4039911}, "travel_mode": "DRIVING"}, {"distance": {"text": "1.6 km", "value": 1607}, "duration": {"text": "3 mins", "value": 155}, "end_location": {"lat": 51.5364139, "lng": -0.390802}, "html_instructions": "At <b>Polish Air Force Mem Roundabout</b>, take the <b>2nd</b> exit onto <b>W End Rd</b>/<wbr/><b>Ruislip Rd</b>/<wbr/><b>A4180</b><div style=\"font-size:0.9em\">Continue to follow Ruislip Rd/<wbr/>A4180</div>", "maneuver": "roundabout-left", "polyline": {"points": "{asyHt~lA?C?I?I@G?I@I@G@G@E@C@E@C@EBIDGBGDEBCLIHAHAH@JBFDHHFHDLBFNDD@D?TCFCVMNI\\MNG\\Kl@OdBc@@?f@M^IHCLCvAWzB[`@GD?NCj@GPCJA@AHDHArAQDANAZATCZAb@CD?FANATE`AI`@Gx@GfAMNA|CYl@IJA`@IHCNEDA\\KZOJGHGRMLMJGRSHILOJKHK@CZe@@CJQJS@?Vk@\\y@`@cAf@kAHSh@}A|@cCTo@X_AR{@T}@BMDMDMHSLWP_@JWHMDIBIDS@E@EBE@EBCBGBGBGBGN_@"}, "start_location": {"lat": 51.5486212, "lng": -0.3993082}, "travel_mode": "DRIVING"}, {"distance": {"text": "1.3 km", "value": 1255}, "duration": {"text": "2 mins", "value": 110}, "end_location": {"lat": 51.5256789, "lng": -0.3907344}, "html_instructions": "At the roundabout, take the <b>3rd</b> exit onto <b>The Pkwy</b>/<wbr/><b>A312</b>", "maneuver": "roundabout-left", "polyline": {"points": "qupyHnikA?C?E?C?C?E?C?E@M@KBI@IBIDIBIDEDGDE@ADABABADAB?B?B?B?F?FBFBFDFDDFDHBHBD@B@D@B@D@DDFBBDDBBBBDBBB@?@@@@@?B@B@B@Z@B?NAPA\\EVEBA@?ZKNCl@Sz@W^Mf@Od@KJCZI~@Ql@Kp@KXC^Gh@ELAXCf@Cd@Cv@Ch@Ah@?|@?|@B`ABr@D@?H@bCJdBH|@Hh@H`AP|@R`A\\LDTH`@NLHVL`@RNFLBF?F?V?"}, "start_location": {"lat": 51.5364139, "lng": -0.390802}, "travel_mode": "DRIVING"}, {"distance": {"text": "3.4 km", "value": 3362}, "duration": {"text": "4 mins", "value": 219}, "end_location": {"lat": 51.4983336, "lng": -0.4088897}, "html_instructions": "At the roundabout, take the <b>2nd</b> exit and stay on <b>The Pkwy</b>/<wbr/><b>A312</b>", "maneuver": "roundabout-left", "polyline": {"points": "ornyH`ikABCBCDCBCDABADAB?H@D@FBDBDDBDDF@HBB@D@B@D@D?D@B@DL^FNHNDHB@HJTPv@l@BBv@p@TRVTJJn@n@BBBBn@t@PP`AlADF\\b@RZJN<PERSON>@bAbA`BNVVf@d@v@P\\`@t@^p@j@|@h@x@NRX^RVNRl@r@^f@NNHHh@j@NPXVr@n@|@|@PPlAfA\\Z\\Z\\ZFFx@t@\\ZbA~@XXHF@@JJHH^ZfBrAt@h@dBfAXRBB\\PjAj@z@`@f@RTHb@NJBtA^fAVz@^`@NNFjA^rBr@`@L|Ah@bBj@t@VXJF@LDdCz@p@Tl@R~@Zl@Rx@Xh@P`@LJDxAf@jBl@b@N~@ZdA^`Bh@j@Rh@Pp@T@@x@Vt@VNDLDj@PHDVHf@P`@LXJj@PHDpAb@ZJh@PdA\\nAb@x@XTHLD`@L\\LRFPFj@Rz@Xl@R|Ad@TFJ@B@D@F@D?B?DABAFA@ABCBAVU"}, "start_location": {"lat": 51.5256789, "lng": -0.3907344}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.6 km", "value": 633}, "duration": {"text": "2 mins", "value": 98}, "end_location": {"lat": 51.4928404, "lng": -0.4099573}, "html_instructions": "At the roundabout, take the <b>2nd</b> exit and stay on <b>The Pkwy</b>/<wbr/><b>A312</b>", "maneuver": "roundabout-left", "polyline": {"points": "qgiyHpznA@CBGBC@ADEDEDCDAFAD?D?F@D@DBDDDD@?BDBFBBRb@@DLRBBFFFFJDB@@?D@H?LAvAJJ@z@LB?NBN@TBp@FJ@`@BXBl@FJ@t@FP@VBB?^Bn@@pBFvAN\\BRDVBJ?B?H?J?"}, "start_location": {"lat": 51.4983336, "lng": -0.4088897}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.5 km", "value": 478}, "duration": {"text": "1 min", "value": 59}, "end_location": {"lat": 51.4911878, "lng": -0.4036982}, "html_instructions": "At <b>Cranford Parkway Interchange</b>, take the <b>1st</b> exit onto the <b>M4</b> ramp to <b>Central London</b>", "maneuver": "roundabout-left", "polyline": {"points": "gehyHfaoAH[FQHMJa@DQDQ@M@M@M@MDc@VkC@_@Be@ZaCBOBWZgCHc@BSHc@BSLo@?CHc@^eBp@yCNk@?ABANI"}, "start_location": {"lat": 51.4928404, "lng": -0.4099573}, "travel_mode": "DRIVING"}, {"distance": {"text": "9.3 km", "value": 9329}, "duration": {"text": "9 mins", "value": 522}, "end_location": {"lat": 51.4896918, "lng": -0.2779332}, "html_instructions": "Merge onto <b>M4</b>", "maneuver": "merge", "polyline": {"points": "}zgyHbzmAXyAlAaHb@_Cl@kDtAcIJo@t@_ErAsHD[Hi@XgB^iCf@cEX_CP{B?KR{B?GPwCHuADgA?IF{AFgBB_A?]@a@@a@?k@?C@]?[@y@?c@@m@A_B?w@AgAA{A?EC_BAME{AEqACm@EcA[}FOuBScCY_DO}AAMG{@YuCEe@MoAOqBGk@C[KwAGy@IoAEk@OuBAO_@wGEu@Es@Eq@M_CIgAGgAUwDOsBEm@C_@C]Gs@QqBSgBIs@Ge@E]Gi@YoBGc@QaAQeAo@iDAKS_AS}@YkASy@Oi@CMSs@YeAK[a@uAEOgAkDOe@Y_AGSiAuDo@wBo@cCGWWkA]wACOS_AWkAMo@Km@Ko@CMG_@G[O}@QmAGi@Kq@MeAK}@Gg@Is@CYI}@KaAIiAAKEk@AK?CEk@G_A?EGw@E}@Cc@GyAEcACaAE}@GuBG_CI}CGwBAi@Co@GqBG_CEgBE_BE{ACq@?YGkBGkCCi@Ai@?AEgBEsAEqACiAAg@EwAAO?UCoAAMAq@As@AsAAq@?e@?K@m@?q@@c@?Y@s@FeCDs@Bq@HgABW@QH_AFo@Hs@Fm@Hg@Ho@F_@BMFg@@ADWHe@PaAJc@BQDSDQPq@Nk@Ja@V_ADQ`@oANk@\\kA^mAPm@r@_Cr@cC@Eb@uAV{@DOpBcG^kAf@_BzAeFZcARq@h@eBh@iB\\gATs@Rs@DK?CJ]Pi@J[H[X{@ZgALe@Ng@Jc@@ENi@RcATiAL{@L_A@AHu@Hw@Hw@Dq@Fu@@Y@YB]?MBi@@y@@a@@]?[?G?qA?e@As@?KC_AA_@AYC]Co@C]Ec@C]C[C[ACC[M_AEYGk@Ga@UmAG]GYGWMk@Mg@EOMg@e@eBa@aBYeAI]Mi@Qw@EKCK?COs@Ou@Ii@Kw@Ik@E_@Gk@C_@Go@Cm@Ck@AACu@Am@Ay@CgB?S?CCgCAiC?EAqC?U?U?}@?i@?K?Y@cE?]@kB?_@?w@?[@kD@y@?eC?M@Y?W?a@?y@@y@@iA?i@@u@Aa@@WA_@?e@AWAWAWAQGu@Go@E[AICMKm@Kc@Sw@CM[gAWgA]mAi@mBKe@_@wAMa@G]EMEUCSEYCUAOAOAM?MAO?M?Q?O?K?C@Q?S@O@SBYBSDe@Dg@Hk@Fm@Da@Hm@Fi@Hi@PaALs@HYH_@FS?CNk@Lc@L_@Ne@\\}@L_@Rk@L_@Pe@\\_Av@{B|@iCTm@To@Z_AX{@Po@X}@Z}@Po@HWH_@"}, "start_location": {"lat": 51.4911878, "lng": -0.4036982}, "travel_mode": "DRIVING"}, {"distance": {"text": "1.8 km", "value": 1797}, "duration": {"text": "3 mins", "value": 178}, "end_location": {"lat": 51.4872042, "lng": -0.2526937}, "html_instructions": "Continue onto <b>Great West Rd</b>/<wbr/><b>A4</b><div style=\"font-size:0.9em\">Continue to follow A4</div>", "polyline": {"points": "qqgyH`hu@f@wBXmABE@EZoA?ETy@FWDWJm@Ho@PmANkAd@sDHi@BMLuAJ}ABk@@IBy@BgAFcC@i@B]@YHiARcBLy@Jw@Da@Fa@Du@Bg@Dk@B]@Q@a@?O?c@@k@B]?W?iA@kA?Y@mAAQ@oF?}A?{F?yD?U?iE?iJ?K?W?K@g@?[@[@]B{@Bi@HwAHsAJwAJeAFu@X_DFw@J_BBe@@S@W?G?G?MAQAOAMCKCMAKAGGa@"}, "start_location": {"lat": 51.4896918, "lng": -0.2779332}, "travel_mode": "DRIVING"}, {"distance": {"text": "3.3 km", "value": 3312}, "duration": {"text": "5 mins", "value": 301}, "end_location": {"lat": 51.4909384, "lng": -0.2069903}, "html_instructions": "At <b>Hogarth Roundabout</b>, take the <b>2nd</b> exit onto <b>Great West Rd Chiswick</b>/<wbr/><b>A4</b><div style=\"font-size:0.9em\">Continue to follow A4</div>", "maneuver": "roundabout-left", "polyline": {"points": "_bgyHhjp@EG?ACKAEAKCM?MAIAa@CWAIAIAEEQQe@Ys@CIg@mA_@eAOe@MYw@gC_AyC?AsAmEOe@YcAOe@CKEKUu@Mc@Sm@i@mBSq@_@qAKc@Oo@Ke@Q}@Ow@SoAMw@Ge@CMCSQiACKKs@SqAIw@Ku@Ei@?AEk@Cg@Cg@AS?MCe@Aq@?EAmA?UAgCAkC?e@@u@?K@aBBiB?u@@u@@e@Bw@@[HmBHyAHiBVgGFg@@Y?K@I?M@K@S?Q?E?K@k@Ao@?E?]Co@?KCiAA_@Aa@Cu@C{@?OAO?MAYAU?SAK?SAYAcACo@C_A?IASAo@Ae@Aq@Ak@Ac@?YAo@Ao@?m@?c@Ae@?[?g@A}@?y@?]AY?o@?i@Ay@?E?{@As@?M?S?u@Au@?E?_@?K?WA_@?s@AkC?S?C?k@@i@Bi@?A@[Dm@BWFg@Fi@?EBOJm@L_AN_AFg@D[@MBW@K@M@O@S?K@a@?W?QAY?]AcAAu@?S?Y?c@A_@@Y?C?W?c@@y@?Y@W?Q@c@@o@@S?MDs@Bc@Bq@FcADs@Bq@HuA@]@i@@iA?u@?c@?u@Co@EcACa@Ee@CYIw@Iu@C]]gDCY"}, "start_location": {"lat": 51.4872042, "lng": -0.2526937}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.7 km", "value": 707}, "duration": {"text": "2 mins", "value": 147}, "end_location": {"lat": 51.4855732, "lng": -0.2025609}, "html_instructions": "Turn <b>right</b> onto <b>North End Rd</b>/<wbr/><b>B317</b>", "maneuver": "turn-right", "polyline": {"points": "kygyHtlg@C]RMx@M@DXMFCNGPIh@ULG@?JGTK@ARKNKZUZSFEFEZ[TW@CBCBEBG@CDKFSBMR}@Je@TaA@GBG@I@E@A?C@A@C@C@C@CBEBCJKDAHGNKNEVONKTMPKd@WHEd@SPIDA`Aa@DCl@WPIJEJGLCHALCJADC`@QFCPK`@S"}, "start_location": {"lat": 51.4909384, "lng": -0.2069903}, "travel_mode": "DRIVING"}, {"distance": {"text": "19 m", "value": 19}, "duration": {"text": "1 min", "value": 35}, "end_location": {"lat": 51.4854728, "lng": -0.2025358}, "html_instructions": "At the roundabout, take the <b>2nd</b> exit onto <b>Lillie Rd</b>/<wbr/><b>A3218</b>", "maneuver": "roundabout-left", "polyline": {"points": "ywfyH~pf@A??A?A?A?A?A?A@A?A@??A@?@??@@??@@??@?@HD"}, "start_location": {"lat": 51.4855732, "lng": -0.2025609}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.7 km", "value": 670}, "duration": {"text": "3 mins", "value": 175}, "end_location": {"lat": 51.4800222, "lng": -0.1986856}, "html_instructions": "At the roundabout, take the <b>1st</b> exit onto <b>North End Rd</b>/<wbr/><b>B317</b><div style=\"font-size:0.9em\">Continue to follow North End Rd</div>", "maneuver": "roundabout-left", "polyline": {"points": "ewfyHzpf@?A?A@??A?A@?@?@?@??@f@YLINMVQLK^WBCHGTQJELMPMp@e@n@c@XWFGFENKVULIhA{@LIx@m@VUdAs@PQJINK\\]JMJQHIDGDGJK`AQRETCHCZGh@IRELCBE@CFGHAHELEJEBADI"}, "start_location": {"lat": 51.4854728, "lng": -0.2025358}, "travel_mode": "DRIVING"}, {"distance": {"text": "57 m", "value": 57}, "duration": {"text": "1 min", "value": 18}, "end_location": {"lat": 51.4802373, "lng": -0.1989233}, "html_instructions": "At the roundabout, take the <b>4th</b> exit onto <b>North End Rd</b>/<wbr/><b>A3219</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "roundabout-left", "polyline": {"points": "cueyHxxe@?AA??AA??A?AA??A?A?A?A@??A?A@??A@??A@?@??@@??@@??@?@@??@?@?@?@A??@?@A??@A??@A?A?EH?B?@ABABABGDEBQF"}, "start_location": {"lat": 51.4800222, "lng": -0.1986856}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.8 km", "value": 821}, "duration": {"text": "3 mins", "value": 179}, "end_address": "612 Fulham Rd., London SW6 5RP, UK", "end_location": {"lat": 51.47677179999999, "lng": -0.2033467}, "start_address": "21 Fulham Rd., London SW6 5NR, UK", "start_location": {"lat": 51.4802373, "lng": -0.1989233}, "steps": [{"distance": {"text": "58 m", "value": 58}, "duration": {"text": "1 min", "value": 17}, "end_location": {"lat": 51.4807461, "lng": -0.1990441}, "html_instructions": "Head <b>north</b> on <b>North End Rd</b>/<wbr/><b>A3219</b> toward <b>Dawes Rd</b>", "polyline": {"points": "oveyH<PERSON><PERSON>@A@IBE?GAMBSDi@H"}, "start_location": {"lat": 51.4802373, "lng": -0.1989233}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 237}, "duration": {"text": "1 min", "value": 38}, "end_location": {"lat": 51.4800532, "lng": -0.2022487}, "html_instructions": "Turn <b>left</b> onto <b>Dawes Rd</b>/<wbr/><b>A3219</b>", "maneuver": "turn-left", "polyline": {"points": "uyeyH~ze@Dx@Dl@?HBr@?BBh@D^D^BPBNDTLn@@BPz@DNXtARdABJ@F@D"}, "start_location": {"lat": 51.4807461, "lng": -0.1990441}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 274}, "duration": {"text": "1 min", "value": 71}, "end_location": {"lat": 51.4785131, "lng": -0.2052691}, "html_instructions": "Turn <b>left</b> onto <b>Bishop's Rd</b>", "maneuver": "turn-left", "polyline": {"points": "iueyH`of@D?B@B@@@BDBHHRzApDZt@DHLTBDJTBJz@fBFLDLJXDNBPBNBNH`@"}, "start_location": {"lat": 51.4800532, "lng": -0.2022487}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 227}, "duration": {"text": "1 min", "value": 23}, "end_location": {"lat": 51.4767259, "lng": -0.2036946}, "html_instructions": "Turn <b>left</b> onto <b>Winchendon Rd</b>", "maneuver": "turn-left", "polyline": {"points": "ukeyH|ag@jIeHFEFGFG"}, "start_location": {"lat": 51.4785131, "lng": -0.2052691}, "travel_mode": "DRIVING"}, {"distance": {"text": "25 m", "value": 25}, "duration": {"text": "1 min", "value": 30}, "end_location": {"lat": 51.47677179999999, "lng": -0.2033467}, "html_instructions": "Turn <b>left</b> onto <b>Fulham Rd.</b>/<wbr/><b>A304</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "q`eyH`xf@ASAOAOAO"}, "start_location": {"lat": 51.4767259, "lng": -0.2036946}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.3 km", "value": 256}, "duration": {"text": "1 min", "value": 45}, "end_address": "48 St Maur Rd, London SW6 4DP, UK", "end_location": {"lat": 51.475261, "lng": -0.2027638}, "start_address": "612 Fulham Rd., London SW6 5RP, UK", "start_location": {"lat": 51.47677179999999, "lng": -0.2033467}, "steps": [{"distance": {"text": "87 m", "value": 87}, "duration": {"text": "1 min", "value": 14}, "end_location": {"lat": 51.4763853, "lng": -0.2044028}, "html_instructions": "Head <b>west</b> on <b>Fulham Rd.</b>/<wbr/><b>A304</b> toward <b><PERSON><PERSON><PERSON>'s Cross Rd</b>", "polyline": {"points": "y`eyH|uf@@N@N@N@RFNDLDJDJBJ@BDLP`@JV"}, "start_location": {"lat": 51.47677179999999, "lng": -0.2033467}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 169}, "duration": {"text": "1 min", "value": 31}, "end_location": {"lat": 51.475261, "lng": -0.2027638}, "html_instructions": "Turn <b>left</b> onto <b>St Maur Rd</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "m~dyHn|f@RYHKbAeBz@{Ar@gALS@C"}, "start_location": {"lat": 51.4763853, "lng": -0.2044028}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.4 km", "value": 410}, "duration": {"text": "2 mins", "value": 99}, "end_address": "688 Fulham Rd., London SW6 5SA, UK", "end_location": {"lat": 51.4747467, "lng": -0.206507}, "start_address": "48 St Maur Rd, London SW6 4DP, UK", "start_location": {"lat": 51.475261, "lng": -0.2027638}, "steps": [{"distance": {"text": "0.2 km", "value": 169}, "duration": {"text": "1 min", "value": 36}, "end_location": {"lat": 51.4763853, "lng": -0.2044028}, "html_instructions": "Head <b>northwest</b> on <b>St Maur Rd</b> toward <b>Fulham Rd.</b>/<wbr/><b>A304</b>", "polyline": {"points": "kwdyHfrf@ABMRs@fA{@zAcAdBIJSX"}, "start_location": {"lat": 51.475261, "lng": -0.2027638}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 241}, "duration": {"text": "1 min", "value": 63}, "end_location": {"lat": 51.4747467, "lng": -0.206507}, "html_instructions": "Turn <b>left</b> onto <b>Fulham Rd.</b>/<wbr/><b>A304</b><div style=\"font-size:0.9em\">Destination will be on the right</div>", "maneuver": "turn-left", "polyline": {"points": "m~dyHn|f@Zt@^z@JVVn@FN@DDLBDBFFN?DHNLR@BFHHHDDFDFDRHNHB@h@Vb@THHDF"}, "start_location": {"lat": 51.4763853, "lng": -0.2044028}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "1.0 km", "value": 1014}, "duration": {"text": "4 mins", "value": 217}, "end_address": "8A Fulham Broadway, London SW6 1AA, UK", "end_location": {"lat": 51.4802105, "lng": -0.1964723}, "start_address": "688 Fulham Rd., London SW6 5SA, UK", "start_location": {"lat": 51.4747467, "lng": -0.206507}, "steps": [{"distance": {"text": "1.0 km", "value": 1014}, "duration": {"text": "4 mins", "value": 217}, "end_location": {"lat": 51.4802105, "lng": -0.1964723}, "html_instructions": "Head <b>northeast</b> on <b>Fulham Rd.</b>/<wbr/><b>A304</b> toward <b>Dancer Rd</b><div style=\"font-size:0.9em\">Go through 1 roundabout</div><div style=\"font-size:0.9em\">Destination will be on the right</div>", "polyline": {"points": "etdyHtig@EGIIc@Ui@WCAOISIGEGEEEIIGIACMSIO?EGOCGCEEMAEGOWo@KW_@{@[u@KWQa@EMACCKEKEKEMGOASAOAOAQEk@?CAGAMAOAMAIK[EUCIMa@ACMe@GOEOKWGMEIGMEAEAGECACACAQEECGAEEGCEECAAACECCGGCEEKIMIMKO?AMSYi@CGGKEGEGIKGGAAEI{@}@QSCAGGMS]e@_@k@SYEG_@k@EGGKSYIKECGCCA?@A??@?@A??@A??@A?A??AA??AA??A?AA??A?A?A?A@??A?A@??A@?GKOWOWQ]GMEG?CEGAICGAG?GAQ?CAO?E?I@K?K@KDYNs@DWF[Jk@"}, "start_location": {"lat": 51.4747467, "lng": -0.206507}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "1.0 km", "value": 1028}, "duration": {"text": "5 mins", "value": 297}, "end_address": "Mirabel House, Wandsworth Bridge Rd., London SW6 2TP, UK", "end_location": {"lat": 51.47308049999999, "lng": -0.1910151}, "start_address": "8A Fulham Broadway, London SW6 1AA, UK", "start_location": {"lat": 51.4802105, "lng": -0.1964723}, "steps": [{"distance": {"text": "58 m", "value": 58}, "duration": {"text": "1 min", "value": 19}, "end_location": {"lat": 51.4800553, "lng": -0.1956868}, "html_instructions": "Head <b>southeast</b> on <b>Fulham Rd.</b>/<wbr/><b>Fulham Broadway</b>/<wbr/><b>A304</b> toward <b>Vanston Pl</b>", "polyline": {"points": "iveyH|je@Ha@NaAAM@G?E@E@U"}, "start_location": {"lat": 51.4802105, "lng": -0.1964723}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.4 km", "value": 437}, "duration": {"text": "2 mins", "value": 110}, "end_location": {"lat": 51.47711820000001, "lng": -0.1916452}, "html_instructions": "Turn <b>right</b> onto <b>Harwood Rd</b>/<wbr/><b>B318</b>", "maneuver": "turn-right", "polyline": {"points": "kueyH`fe@?C?QPAJEBAB?@AB?@?FA^m@LS\\i@\\g@HKT_@^i@Vc@LSh@_Aj@aAb@q@LUdAgBR[j@aALUPYVa@"}, "start_location": {"lat": 51.4800553, "lng": -0.1956868}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 112}, "duration": {"text": "1 min", "value": 76}, "end_location": {"lat": 51.4763994, "lng": -0.1927738}, "html_instructions": "Turn <b>right</b> onto <b>New Kings Rd</b>/<wbr/><b>A308</b>", "maneuver": "turn-right", "polyline": {"points": "_ceyHxld@DHBDHRJRd@z@NXp@fAFH"}, "start_location": {"lat": 51.47711820000001, "lng": -0.1916452}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 313}, "duration": {"text": "1 min", "value": 53}, "end_location": {"lat": 51.47364289999999, "lng": -0.1918812}, "html_instructions": "Turn <b>left</b> onto <b>Wandsworth Bridge Rd.</b>/<wbr/><b>A217</b>", "maneuver": "turn-left", "polyline": {"points": "o~dyHxsd@x@?LEdCs@b@MVGPGr@SLEfAWPEb@IjAW"}, "start_location": {"lat": 51.4763994, "lng": -0.1927738}, "travel_mode": "DRIVING"}, {"distance": {"text": "32 m", "value": 32}, "duration": {"text": "1 min", "value": 5}, "end_location": {"lat": 51.473701, "lng": -0.1914353}, "html_instructions": "Turn <b>left</b> onto <b>Sandilands Rd</b>", "maneuver": "turn-left", "polyline": {"points": "gmdyHfnd@Eo@Eg@"}, "start_location": {"lat": 51.47364289999999, "lng": -0.1918812}, "travel_mode": "DRIVING"}, {"distance": {"text": "48 m", "value": 48}, "duration": {"text": "1 min", "value": 16}, "end_location": {"lat": 51.4733252, "lng": -0.1911034}, "html_instructions": "Turn <b>right</b> to stay on <b>Sandilands Rd</b>", "maneuver": "turn-right", "polyline": {"points": "smdyHnkd@d@a@RQBC@A@ABABE"}, "start_location": {"lat": 51.473701, "lng": -0.1914353}, "travel_mode": "DRIVING"}, {"distance": {"text": "28 m", "value": 28}, "duration": {"text": "1 min", "value": 18}, "end_location": {"lat": 51.47308049999999, "lng": -0.1910151}, "html_instructions": "Continue onto <b>Pearscroft Rd</b>", "polyline": {"points": "ikdyHjid@FABABAHADABABAHA"}, "start_location": {"lat": 51.4733252, "lng": -0.1911034}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.9 km", "value": 872}, "duration": {"text": "4 mins", "value": 246}, "end_address": "The Finishing Touch, 201 New Kings Rd, London SW6 4SR, UK", "end_location": {"lat": 51.4732142, "lng": -0.1977407}, "start_address": "Mirabel House, Wandsworth Bridge Rd., London SW6 2TP, UK", "start_location": {"lat": 51.47308049999999, "lng": -0.1910151}, "steps": [{"distance": {"text": "28 m", "value": 28}, "duration": {"text": "1 min", "value": 5}, "end_location": {"lat": 51.4733252, "lng": -0.1911034}, "html_instructions": "Head <b>north</b> on <b>Pearscroft Rd</b> toward <b>Sandilands Rd</b>", "polyline": {"points": "widyHzhd@I@C@C@E@I@C@C@G@"}, "start_location": {"lat": 51.47308049999999, "lng": -0.1910151}, "travel_mode": "DRIVING"}, {"distance": {"text": "79 m", "value": 79}, "duration": {"text": "1 min", "value": 36}, "end_location": {"lat": 51.47364289999999, "lng": -0.1918812}, "html_instructions": "Continue straight onto <b>Sandilands Rd</b>", "maneuver": "straight", "polyline": {"points": "ik<PERSON><PERSON><PERSON>@CDC@A@A@CBSPe@`@Df@Dn@"}, "start_location": {"lat": 51.4733252, "lng": -0.1911034}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 273}, "duration": {"text": "2 mins", "value": 131}, "end_location": {"lat": 51.4760392, "lng": -0.192738}, "html_instructions": "Turn <b>right</b> onto <b>Wandsworth Bridge Rd.</b>/<wbr/><b>A217</b>", "maneuver": "turn-right", "polyline": {"points": "gmdyHfnd@kAVc@HQDgAVMDs@RQFWFc@LeCr@"}, "start_location": {"lat": 51.47364289999999, "lng": -0.1918812}, "travel_mode": "DRIVING"}, {"distance": {"text": "28 m", "value": 28}, "duration": {"text": "1 min", "value": 7}, "end_location": {"lat": 51.4761664, "lng": -0.1930791}, "html_instructions": "Turn <b>left</b> to stay on <b>Wandsworth Bridge Rd.</b>/<wbr/><b>A217</b>", "maneuver": "turn-left", "polyline": {"points": "g|dyHrsd@KPEL?DCJCP"}, "start_location": {"lat": 51.4760392, "lng": -0.192738}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.5 km", "value": 464}, "duration": {"text": "1 min", "value": 67}, "end_location": {"lat": 51.4732142, "lng": -0.1977407}, "html_instructions": "Turn <b>left</b> onto <b>New Kings Rd</b>/<wbr/><b>A308</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "a}dyHvud@V^DDBDFLJNHNJVFNFLJTNXBFP^JRJLRRl@l@LLTTLLXXBFFHFLDLR`@?@Rb@Zt@z@jB^|@j@pALXHRLXJRBF"}, "start_location": {"lat": 51.4761664, "lng": -0.1930791}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "1.3 km", "value": 1321}, "duration": {"text": "5 mins", "value": 283}, "end_address": "511 King's Rd, London SW10 0TX, UK", "end_location": {"lat": 51.4806186, "lng": -0.1839047}, "start_address": "The Finishing Touch, 201 New Kings Rd, London SW6 4SR, UK", "start_location": {"lat": 51.4732142, "lng": -0.1977407}, "steps": [{"distance": {"text": "1.3 km", "value": 1258}, "duration": {"text": "4 mins", "value": 230}, "end_location": {"lat": 51.4807663, "lng": -0.1844757}, "html_instructions": "Head <b>northeast</b> on <b>New Kings Rd</b>/<wbr/><b>A308</b> toward <b>Molesford Rd</b><div style=\"font-size:0.9em\">Continue to follow A308</div>", "polyline": {"points": "qjdy<PERSON><PERSON><PERSON>@CGKSMYISMYk@qA_@}@{@kB[u@Sc@?ASa@EMGMGICGYYMMUUMMm@m@SSKMKSQ_@CGOYKUGMGOKWIOKOGMCEEEW_@S[Ya@GIq@gAOYe@{@KSISCEEIGQMWGMS_@EKIQEICGAEACACAE?EAE?G?M?OAc@AEAGAEAEIWGMEICIIOOYMWMUCEMSKUKSACy@}Aw@}A_@s@a@y@KSCGEIS_@AAACACMUIQIOOWIQKQQ]MWWg@AEKUEIACO_@CKACGOGQGUISCIW}@I[Qm@Qo@"}, "start_location": {"lat": 51.4732142, "lng": -0.1977407}, "travel_mode": "DRIVING"}, {"distance": {"text": "33 m", "value": 33}, "duration": {"text": "1 min", "value": 42}, "end_location": {"lat": 51.4805004, "lng": -0.1842718}, "html_instructions": "Turn <b>right</b> onto <b>Thorndike Cl</b>", "maneuver": "turn-right", "polyline": {"points": "yyeyH~_c@t@i@"}, "start_location": {"lat": 51.4807663, "lng": -0.1844757}, "travel_mode": "DRIVING"}, {"distance": {"text": "30 m", "value": 30}, "duration": {"text": "1 min", "value": 11}, "end_location": {"lat": 51.4806186, "lng": -0.1839047}, "html_instructions": "Turn <b>left</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "cxeyHt~b@@?YiA"}, "start_location": {"lat": 51.4805004, "lng": -0.1842718}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.3 km", "value": 338}, "duration": {"text": "3 mins", "value": 160}, "end_address": "471-487 King's Rd, London SW10 0LU, UK", "end_location": {"lat": 51.4818176, "lng": -0.1808934}, "start_address": "511 King's Rd, London SW10 0TX, UK", "start_location": {"lat": 51.4806186, "lng": -0.1839047}, "steps": [{"distance": {"text": "29 m", "value": 29}, "duration": {"text": "1 min", "value": 7}, "end_location": {"lat": 51.4804888, "lng": -0.1842658}, "html_instructions": "Head <b>southwest</b> toward <b>Thorndike Cl</b>", "polyline": {"points": "{xeyHj|b@XhA"}, "start_location": {"lat": 51.4806186, "lng": -0.1839047}, "travel_mode": "DRIVING"}, {"distance": {"text": "34 m", "value": 34}, "duration": {"text": "1 min", "value": 12}, "end_location": {"lat": 51.4807663, "lng": -0.1844757}, "html_instructions": "Turn <b>right</b> onto <b>Thorndike Cl</b>", "maneuver": "turn-right", "polyline": {"points": "axeyHt~b@A?u@h@"}, "start_location": {"lat": 51.4804888, "lng": -0.1842658}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 275}, "duration": {"text": "2 mins", "value": 141}, "end_location": {"lat": 51.4818176, "lng": -0.1808934}, "html_instructions": "Turn <b>right</b> onto <b>King's Rd</b>/<wbr/><b>A308</b><div style=\"font-size:0.9em\">Continue to follow King's Rd</div><div style=\"font-size:0.9em\">Destination will be on the right</div>", "maneuver": "turn-right", "polyline": {"points": "yyeyH~_c@Ke@K[Mg@COOg@Mm@EKAEAGCIKe@G][oACKCIQgAMy@CI?ICWIo@COEWAO?AEW"}, "start_location": {"lat": 51.4807663, "lng": -0.1844757}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.2 km", "value": 171}, "duration": {"text": "1 min", "value": 28}, "end_address": "429 King's Rd, London SW10 0LR, UK", "end_location": {"lat": 51.4825345, "lng": -0.1787306}, "start_address": "471-487 King's Rd, London SW10 0LU, UK", "start_location": {"lat": 51.4818176, "lng": -0.1808934}, "steps": [{"distance": {"text": "0.2 km", "value": 171}, "duration": {"text": "1 min", "value": 28}, "end_location": {"lat": 51.4825345, "lng": -0.1787306}, "html_instructions": "Head <b>east</b> on <b>King's Rd</b>/<wbr/><b>A3217</b> toward <b>Slaidburn St</b><div style=\"font-size:0.9em\">Destination will be on the right</div>", "polyline": {"points": "k`fyHpib@AK?G?EAGCICMIc@I[CMAECICIEMEQGWGQCIAECMCICK@I[eAW_A"}, "start_location": {"lat": 51.4818176, "lng": -0.1808934}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.9 km", "value": 889}, "duration": {"text": "4 mins", "value": 219}, "end_address": "Chelsea and Westminster Hospital Private Patients Dept, London SW10 9NH, UK", "end_location": {"lat": 51.483977, "lng": -0.1820259}, "start_address": "429 King's Rd, London SW10 0LR, UK", "start_location": {"lat": 51.4825345, "lng": -0.1787306}, "steps": [{"distance": {"text": "77 m", "value": 77}, "duration": {"text": "1 min", "value": 11}, "end_location": {"lat": 51.4829097, "lng": -0.1778}, "html_instructions": "Head <b>northeast</b> on <b>King's Rd</b>/<wbr/><b>A3217</b> toward <b>Hobury St</b>", "polyline": {"points": "ydfyH`|a@AAEOEOIYY}@Y_A"}, "start_location": {"lat": 51.4825345, "lng": -0.1787306}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.4 km", "value": 399}, "duration": {"text": "1 min", "value": 81}, "end_location": {"lat": 51.4857472, "lng": -0.1812989}, "html_instructions": "Turn <b>left</b> onto <b>Limerston St</b>", "maneuver": "turn-left", "polyline": {"points": "egfyHfva@WVOPa@d@QRMLcCdCMJIHIJMLKNKRe@t@m@`A]h@iBvCQXGHCFGH"}, "start_location": {"lat": 51.4829097, "lng": -0.1778}, "travel_mode": "DRIVING"}, {"distance": {"text": "62 m", "value": 62}, "duration": {"text": "1 min", "value": 17}, "end_location": {"lat": 51.4853645, "lng": -0.1819419}, "html_instructions": "Turn <b>left</b> onto <b>Fulham Rd.</b>/<wbr/><b>A308</b>", "maneuver": "turn-left", "polyline": {"points": "}xfyHblb@HNZj@f@bA"}, "start_location": {"lat": 51.4857472, "lng": -0.1812989}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 183}, "duration": {"text": "1 min", "value": 58}, "end_location": {"lat": 51.48412039999999, "lng": -0.1802082}, "html_instructions": "Turn <b>left</b> onto <b>Nightingale Pl</b>", "maneuver": "turn-left", "polyline": {"points": "ovfyHbpb@`@q@LQr@gAHMj@}@|AaC"}, "start_location": {"lat": 51.4853645, "lng": -0.1819419}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 168}, "duration": {"text": "1 min", "value": 52}, "end_location": {"lat": 51.483977, "lng": -0.1820259}, "html_instructions": "Turn <b>right</b>", "maneuver": "turn-right", "polyline": {"points": "wnfyHheb@LV^z@LX\\bABDL`@@D?@?F?FADENIP_ApA"}, "start_location": {"lat": 51.48412039999999, "lng": -0.1802082}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "1.4 km", "value": 1421}, "duration": {"text": "6 mins", "value": 344}, "end_address": "76A Old Brompton Rd, South Kensington, London SW7 3LQ, UK", "end_location": {"lat": 51.4927216, "lng": -0.1770005}, "start_address": "Chelsea and Westminster Hospital Private Patients Dept, London SW10 9NH, UK", "start_location": {"lat": 51.483977, "lng": -0.1820259}, "steps": [{"distance": {"text": "65 m", "value": 65}, "duration": {"text": "1 min", "value": 22}, "end_location": {"lat": 51.484083, "lng": -0.1827365}, "html_instructions": "Head <b>northwest</b> toward <b>Netherton Grove</b>", "polyline": {"points": "{mfyHtpb@i@v@AF?D?D?F@F?DDHN`@"}, "start_location": {"lat": 51.483977, "lng": -0.1820259}, "travel_mode": "DRIVING"}, {"distance": {"text": "76 m", "value": 76}, "duration": {"text": "1 min", "value": 30}, "end_location": {"lat": 51.4846284, "lng": -0.1833891}, "html_instructions": "Turn <b>right</b> onto <b>Netherton Grove</b>", "maneuver": "turn-right", "polyline": {"points": "onfyHbub@g@f@GHIHQTIJMRIN"}, "start_location": {"lat": 51.484083, "lng": -0.1827365}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.6 km", "value": 624}, "duration": {"text": "2 mins", "value": 146}, "end_location": {"lat": 51.4884997, "lng": -0.1768824}, "html_instructions": "Turn <b>right</b> onto <b>Fulham Rd.</b>/<wbr/><b>A308</b>", "maneuver": "turn-right", "polyline": {"points": "}qfyHdyb@AGCGKYAEc@cAO_@KUMWGQEEO_@Qg@g@cA[k@IOS_@g@aAQ_@M]KWMWKUk@mAMSS_@KQq@cA]m@Yg@_@m@U_@CGEGGMACMW_@o@y@{Am@kA"}, "start_location": {"lat": 51.4846284, "lng": -0.1833891}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.4 km", "value": 390}, "duration": {"text": "1 min", "value": 72}, "end_location": {"lat": 51.491266, "lng": -0.1800389}, "html_instructions": "Turn <b>left</b> onto <b>Cranley Gardens</b>", "maneuver": "turn-left", "polyline": {"points": "cjgyHnpa@EKQ`@a@|@CJKRKTiAhC_@z@EHEFGDUPQLWP{@n@_@XcAt@aAt@[TWP"}, "start_location": {"lat": 51.4884997, "lng": -0.1768824}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 266}, "duration": {"text": "1 min", "value": 74}, "end_location": {"lat": 51.4927216, "lng": -0.1770005}, "html_instructions": "Turn <b>right</b> onto <b>Old Brompton Rd</b>/<wbr/><b>A3218</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-right", "polyline": {"points": "m{gyHfdb@Mi@CIEMCGKWKYGSISKUEGUk@O]Oa@KYKQ?CO_@[{@[aAGMO_@Sg@GM"}, "start_location": {"lat": 51.491266, "lng": -0.1800389}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.2 km", "value": 203}, "duration": {"text": "1 min", "value": 54}, "end_address": "21 Old Brompton Rd, South Kensington, London SW7 3HZ, UK", "end_location": {"lat": 51.4937072, "lng": -0.1745712}, "start_address": "76A Old Brompton Rd, South Kensington, London SW7 3LQ, UK", "start_location": {"lat": 51.4927216, "lng": -0.1770005}, "steps": [{"distance": {"text": "0.2 km", "value": 203}, "duration": {"text": "1 min", "value": 54}, "end_location": {"lat": 51.4937072, "lng": -0.1745712}, "html_instructions": "Head <b>northeast</b> on <b>Old Brompton Rd</b>/<wbr/><b>A3218</b> toward <b><PERSON><PERSON> Pl</b><div style=\"font-size:0.9em\">Destination will be on the right</div>", "polyline": {"points": "odhyHfqa@ACQa@KWMYM[MYO<PERSON>@Oe@Uq@Qg@Og@KYACEOCOAMAIAEEo@"}, "start_location": {"lat": 51.4927216, "lng": -0.1770005}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "49 m", "value": 49}, "duration": {"text": "1 min", "value": 9}, "end_address": "4 S Kensington Station Arcade, South Kensington, London SW7 2NA, UK", "end_location": {"lat": 51.49411079999999, "lng": -0.174633}, "start_address": "21 Old Brompton Rd, South Kensington, London SW7 3HZ, UK", "start_location": {"lat": 51.4937072, "lng": -0.1745712}, "steps": [{"distance": {"text": "4 m", "value": 4}, "duration": {"text": "1 min", "value": 1}, "end_location": {"lat": 51.4937136, "lng": -0.174516}, "html_instructions": "Head <b>east</b> on <b>Old Brompton Rd</b>/<wbr/><b>A3218</b> toward <b>B304</b>", "polyline": {"points": "ujhyH`ba@?I"}, "start_location": {"lat": 51.4937072, "lng": -0.1745712}, "travel_mode": "DRIVING"}, {"distance": {"text": "45 m", "value": 45}, "duration": {"text": "1 min", "value": 8}, "end_location": {"lat": 51.49411079999999, "lng": -0.174633}, "html_instructions": "Turn <b>left</b> to stay on <b>Old Brompton Rd</b>/<wbr/><b>A3218</b><div style=\"font-size:0.9em\">Destination will be on the right</div>", "maneuver": "turn-left", "polyline": {"points": "ujhyHvaa@O@UD[HMB"}, "start_location": {"lat": 51.4937136, "lng": -0.174516}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.5 km", "value": 492}, "duration": {"text": "2 mins", "value": 126}, "end_address": "203 Fulham Rd., London SW3 6JJ, UK", "end_location": {"lat": 51.49088769999999, "lng": -0.1730459}, "start_address": "4 S Kensington Station Arcade, South Kensington, London SW7 2NA, UK", "start_location": {"lat": 51.49411079999999, "lng": -0.174633}, "steps": [{"distance": {"text": "45 m", "value": 45}, "duration": {"text": "1 min", "value": 6}, "end_location": {"lat": 51.4937136, "lng": -0.174516}, "html_instructions": "Head <b>south</b> on <b>Old Brompton Rd</b>/<wbr/><b>A3218</b> toward <b>B304</b>", "polyline": {"points": "emhyHlba@LCZITENA"}, "start_location": {"lat": 51.49411079999999, "lng": -0.174633}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.3 km", "value": 293}, "duration": {"text": "1 min", "value": 76}, "end_location": {"lat": 51.4918911, "lng": -0.1716542}, "html_instructions": "Turn <b>left</b> onto <b>Old Brompton Rd</b>/<wbr/><b>B304</b><div style=\"font-size:0.9em\">Continue to follow B304</div>", "maneuver": "turn-left", "polyline": {"points": "ujhyH<PERSON>a@?_@?C@SBYBWBIBKBK@G@EBEFKPSFKPWTYNUd@m@r@_APUNQd@m@RY\\c@LOPW"}, "start_location": {"lat": 51.4937136, "lng": -0.174516}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 102}, "duration": {"text": "1 min", "value": 23}, "end_location": {"lat": 51.4912485, "lng": -0.172713}, "html_instructions": "Turn <b>right</b> onto <b>Fulham Rd.</b>/<wbr/><b>A308</b>", "maneuver": "turn-right", "polyline": {"points": "i_hyHxo`@<PERSON>@@HNP\\JRn@nAXj@"}, "start_location": {"lat": 51.4918911, "lng": -0.1716542}, "travel_mode": "DRIVING"}, {"distance": {"text": "52 m", "value": 52}, "duration": {"text": "1 min", "value": 21}, "end_location": {"lat": 51.49088769999999, "lng": -0.1730459}, "html_instructions": "Turn <b>left</b> onto <b><PERSON>l</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "i{gyHlv`@FIBAB?@?b@n@P^"}, "start_location": {"lat": 51.4912485, "lng": -0.172713}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.4 km", "value": 366}, "duration": {"text": "2 mins", "value": 131}, "end_address": "Royal Brompton & Harefield NHS Hospital Mezzaine, Fulham Wing, London SW3 6NP, UK", "end_location": {"lat": 51.4900073, "lng": -0.1710157}, "start_address": "203 Fulham Rd., London SW3 6JJ, UK", "start_location": {"lat": 51.49088769999999, "lng": -0.1730459}, "steps": [{"distance": {"text": "18 m", "value": 18}, "duration": {"text": "1 min", "value": 4}, "end_location": {"lat": 51.4909073, "lng": -0.1732775}, "html_instructions": "Head <b>west</b> on <b>Sumner Pl</b> toward <b>Fulham Rd.</b>/<wbr/><b>A308</b>", "polyline": {"points": "aygyHpx`@@@BJ?DAB?BGP"}, "start_location": {"lat": 51.49088769999999, "lng": -0.1730459}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 114}, "duration": {"text": "1 min", "value": 68}, "end_location": {"lat": 51.4916197, "lng": -0.1720909}, "html_instructions": "Sharp <b>right</b> onto <b>Fulham Rd.</b>/<wbr/><b>A308</b>", "maneuver": "turn-sharp-right", "polyline": {"points": "eygyH~y`@cAqBYk@o@oA"}, "start_location": {"lat": 51.4909073, "lng": -0.1732775}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 198}, "duration": {"text": "1 min", "value": 44}, "end_location": {"lat": 51.4901282, "lng": -0.1705333}, "html_instructions": "Turn <b>right</b> onto <b>Sydney St</b>/<wbr/><b>B304</b>", "maneuver": "turn-right", "polyline": {"points": "s}gyHpr`@NIJK`@e@Z[@AVYp@u@NQX[x@_ALMPQ"}, "start_location": {"lat": 51.4916197, "lng": -0.1720909}, "travel_mode": "DRIVING"}, {"distance": {"text": "36 m", "value": 36}, "duration": {"text": "1 min", "value": 15}, "end_location": {"lat": 51.4900073, "lng": -0.1710157}, "html_instructions": "Turn <b>right</b> onto <b>Cale St</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-right", "polyline": {"points": "itgyHxh`@BPLv@BL@H"}, "start_location": {"lat": 51.4901282, "lng": -0.1705333}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.7 km", "value": 730}, "duration": {"text": "2 mins", "value": 138}, "end_address": "251 King's Rd, London SW3 5EL, UK", "end_location": {"lat": 51.4859786, "lng": -0.1720831}, "start_address": "Royal Brompton & Harefield NHS Hospital Mezzaine, Fulham Wing, London SW3 6NP, UK", "start_location": {"lat": 51.4900073, "lng": -0.1710157}, "steps": [{"distance": {"text": "90 m", "value": 90}, "duration": {"text": "1 min", "value": 19}, "end_location": {"lat": 51.4896199, "lng": -0.1721354}, "html_instructions": "Head <b>southwest</b> on <b>Cale St</b> toward <b>Guthrie St</b>", "polyline": {"points": "qsgyHzk`@Ll@Lr@BLBLTdABJDDBBDD"}, "start_location": {"lat": 51.4900073, "lng": -0.1710157}, "travel_mode": "DRIVING"}, {"distance": {"text": "69 m", "value": 69}, "duration": {"text": "1 min", "value": 14}, "end_location": {"lat": 51.48995739999999, "lng": -0.1728672}, "html_instructions": "Turn <b>right</b> onto <b>Dovehouse St</b>", "maneuver": "turn-right", "polyline": {"points": "cqgyHzr`@AHAF?BA@?BA@MRED[\\EHA@ABABADAD?B?FBN"}, "start_location": {"lat": 51.4896199, "lng": -0.1721354}, "travel_mode": "DRIVING"}, {"distance": {"text": "46 m", "value": 46}, "duration": {"text": "1 min", "value": 9}, "end_location": {"lat": 51.4897431, "lng": -0.1734366}, "html_instructions": "Slight <b>left</b> onto <b>S Parade</b>", "maneuver": "turn-slight-left", "polyline": {"points": "gsgyHlw`@FVHTFVJX@HBF"}, "start_location": {"lat": 51.48995739999999, "lng": -0.1728672}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 196}, "duration": {"text": "1 min", "value": 26}, "end_location": {"lat": 51.48820620000001, "lng": -0.1720606}, "html_instructions": "Turn <b>left</b> onto <b>Chelsea Square</b>", "maneuver": "turn-left", "polyline": {"points": "{qgyH~z`@ZYRQf@c@n@k@ZW\\YNM~AwA"}, "start_location": {"lat": 51.4897431, "lng": -0.1734366}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 189}, "duration": {"text": "1 min", "value": 37}, "end_location": {"lat": 51.4867143, "lng": -0.1707398}, "html_instructions": "Continue straight onto <b>Manresa Rd</b>", "maneuver": "straight", "polyline": {"points": "ihgyHjr`@f@c@^Y`@_@ZWj@e@NOLILMx@s@RO"}, "start_location": {"lat": 51.48820620000001, "lng": -0.1720606}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 122}, "duration": {"text": "1 min", "value": 29}, "end_location": {"lat": 51.48611589999999, "lng": -0.1722195}, "html_instructions": "Turn <b>right</b> onto <b>King's Rd</b>/<wbr/><b>A3217</b>", "maneuver": "turn-right", "polyline": {"points": "}~fyHbj`@@JX`AJ\\L^HR@D@D@BJZBHDNL^J`@"}, "start_location": {"lat": 51.4867143, "lng": -0.1707398}, "travel_mode": "DRIVING"}, {"distance": {"text": "18 m", "value": 18}, "duration": {"text": "1 min", "value": 4}, "end_location": {"lat": 51.4859786, "lng": -0.1720831}, "html_instructions": "Turn <b>left</b> onto <b><PERSON></b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-left", "polyline": {"points": "g{fyHjs`@NQDEDC"}, "start_location": {"lat": 51.48611589999999, "lng": -0.1722195}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.4 km", "value": 366}, "duration": {"text": "3 mins", "value": 154}, "end_address": "43 Dovehouse St, London SW3 6JY, UK", "end_location": {"lat": 51.4880592, "lng": -0.1709573}, "start_address": "251 King's Rd, London SW3 5EL, UK", "start_location": {"lat": 51.4859786, "lng": -0.1720831}, "steps": [{"distance": {"text": "18 m", "value": 18}, "duration": {"text": "1 min", "value": 4}, "end_location": {"lat": 51.48611589999999, "lng": -0.1722195}, "html_instructions": "Head <b>northwest</b> on <b><PERSON></b> toward <b>King's Rd</b>/<wbr/><b>A3217</b>", "polyline": {"points": "kzfyHnr`@EBEDOP"}, "start_location": {"lat": 51.4859786, "lng": -0.1720831}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.2 km", "value": 196}, "duration": {"text": "2 mins", "value": 113}, "end_location": {"lat": 51.48703880000001, "lng": -0.1698103}, "html_instructions": "Turn <b>right</b> onto <b>King's Rd</b>/<wbr/><b>A3217</b>", "maneuver": "turn-right", "polyline": {"points": "g{fyHjs`@Ka@M_@EOCIK[ACAEAEISM_@K]YaAAKMc@CKCIIUOm@I]G]"}, "start_location": {"lat": 51.48611589999999, "lng": -0.1722195}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 139}, "duration": {"text": "1 min", "value": 34}, "end_location": {"lat": 51.4881246, "lng": -0.1707961}, "html_instructions": "Turn <b>left</b> onto <b>Dovehouse St</b>", "maneuver": "turn-left", "polyline": {"points": "_agyHhd`@URABC@i@d@]ZA@CBCBGF]XIJMJIFMJSP"}, "start_location": {"lat": 51.48703880000001, "lng": -0.1698103}, "travel_mode": "DRIVING"}, {"distance": {"text": "13 m", "value": 13}, "duration": {"text": "1 min", "value": 3}, "end_location": {"lat": 51.4880592, "lng": -0.1709573}, "html_instructions": "Turn <b>left</b><div style=\"font-size:0.9em\">Restricted usage road</div><div style=\"font-size:0.9em\">Destination will be on the right</div>", "maneuver": "turn-left", "polyline": {"points": "wggyHnj`@J^"}, "start_location": {"lat": 51.4881246, "lng": -0.1707961}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}, {"distance": {"text": "0.2 km", "value": 168}, "duration": {"text": "1 min", "value": 45}, "end_address": "80 Sydney St, London SW3 6NJ, UK", "end_location": {"lat": 51.4884809, "lng": -0.1694345}, "start_address": "43 Dovehouse St, London SW3 6JY, UK", "start_location": {"lat": 51.4880592, "lng": -0.1709573}, "steps": [{"distance": {"text": "13 m", "value": 13}, "duration": {"text": "1 min", "value": 3}, "end_location": {"lat": 51.4881246, "lng": -0.1707961}, "html_instructions": "Head <b>northeast</b> toward <b>Dovehouse St</b><div style=\"font-size:0.9em\">Restricted usage road</div>", "polyline": {"points": "kggyHnk`@K_@"}, "start_location": {"lat": 51.4880592, "lng": -0.1709573}, "travel_mode": "DRIVING"}, {"distance": {"text": "0.1 km", "value": 117}, "duration": {"text": "1 min", "value": 31}, "end_location": {"lat": 51.4887886, "lng": -0.1696568}, "html_instructions": "Continue onto <b><PERSON><PERSON><PERSON> St</b>", "polyline": {"points": "wggyHnj`@I?AEACACAAACSMKGQMCAECAACCEEAACIAEACKs@CMAKCOIe@"}, "start_location": {"lat": 51.4881246, "lng": -0.1707961}, "travel_mode": "DRIVING"}, {"distance": {"text": "38 m", "value": 38}, "duration": {"text": "1 min", "value": 11}, "end_location": {"lat": 51.4884809, "lng": -0.1694345}, "html_instructions": "Turn <b>right</b> onto <b>Sydney St</b>/<wbr/><b>B304</b><div style=\"font-size:0.9em\">Destination will be on the left</div>", "maneuver": "turn-right", "polyline": {"points": "}kgyHjc`@NKl@a@"}, "start_location": {"lat": 51.4887886, "lng": -0.1696568}, "travel_mode": "DRIVING"}], "traffic_speed_entry": [], "via_waypoint": []}], "overview_polyline": {"points": "ymtyH`yiAu@sDU^jAbGn@jDAb@_C~AiAv@eDzBeAr@[RZ~A^bBXbAHNF?JENTEb@bBpHdAbGRrI@HgAdGqAzD}AnC_@f@@xBj@~AtAbD~EdK`CnHlBnGtB`HfAk@`KoIzEiEpFoEdAo@bAcATyATs@Z[h@@~@v@~DqAzKwBnIu@`RqBbCqA`BsBbDoHjEgNjBsEj@sCd@e@l@DdA`BXN~BMdGeBjHiA~HShLd@fFx@fC|@lCv@d@Un@h@Nh@`CvClDbDxFpHpG|KtEdGfQnPdI`GdHdD`XvIvl@`SdV`IbALr@m@j@Yj@X~@|ArCTlLdArIf@f@m@`@sAj@mHzAaL|BgKh@cBpBaLdE_V|BaNrBiQ|@kPN{Xw@qSwB}ViDyg@uAeSgB_NiDiP}D{MiGaUaBwIwCkW{Acb@qAmf@WoVb@kLbAwIdEsQjRen@jG_TxA{Hn@aI?cQqBmPoFiUw@yIQgL@ia@F_U]{EiB_IuC}KUoCp@aKvAeIzBcHbKyZ`CyJpB{Np@yOjA{K^aSNar@dBiYo@eF}J{ZuCwJqCcM_CeS?i\\jAwXu@ya@Kuf@~AoN@gMd@uPRiLo@aJa@aENk@|AY~BeA|CaC`@gArAgF`IaEjFwB?CBIPFHCnPuL`EoDvCeA~Bo@l@_@A?AAAE@ILBGNIRu@^sAPNdDj@tEnAdG|F`Mt@xCbJ{HGcA`@xB\\~@JVRYlAqB~B{D_CzDmApBFnAjArCn@zAlA|@zAx@m@_@qBiAmDkIiAaGk@iCcAeCcB_AcCaE_HqIEFECkAwCImAr@qE\\_Dl@]dLuQ~AmC\\WLXrBpD`AHrCy@`Cq@jCm@jAWEo@^iAZYj@SWFo@d@_@hADn@kAVu@NuA\\aCp@qCdAERTbA`@r@xA`D|CbDvGbOwGcO}CcDyAaD}FwJuA}D[sBqAeCuGoMcDeHm@qB[iAQo@t@i@WiAVhAu@h@Ke@YcAg@qBS}@oAyGcAaHkBiHo@wBY_AWVq@v@qDrDm@r@eGxJdAhClByChC_El@rA|@dCoApCi@`BTj@o@p@s@~@KFOa@aA_CkCwF{JaRiDqG}@~AaCnFoDrCyDrC[iAaBaEwB}F{EmMMw@W}@q@Np@ONa@HiALi@~A_C`FqG\\CJPfBlDPKv@pA@XkA_BiA{BxAwAzAcBrB{BV`BfAxEOv@q@bAj@dCtEsDnHmG|AeA|@rCt@fCTWUVYaAWy@[_AwAiFQ{@WVmAdAoAhAa@\\J^K_@KECGc@[]Uc@aB?mAl@a@"}, "summary": "M4", "warnings": [], "waypoint_order": [3, 1, 2, 0, 5, 6, 4, 7, 9, 10, 8, 11, 12, 13, 14, 17, 15, 16]}], "status": "OK"}