addict==2.4.0
aiofiles==23.2.1
aiohttp==3.9.5
aiosignal==1.3.1
albucore==0.0.17
albumentations==1.4.16
alembic==1.13.2
altair==5.3.0
annotated-types==0.7.0
ansicolors==1.1.8
anyascii==0.3.2
anyio==4.4.0
apache-airflow==2.9.2
apache-airflow-providers-common-io==1.3.2
apache-airflow-providers-common-sql==1.14.1
apache-airflow-providers-fab==1.2.0
apache-airflow-providers-ftp==3.10.0
apache-airflow-providers-http==4.12.0
apache-airflow-providers-imap==3.6.1
apache-airflow-providers-smtp==1.7.1
apache-airflow-providers-sqlite==3.8.1
apispec==6.6.1
appnope==0.1.4
argcomplete==3.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.8.1
asn1crypto==1.5.1
astroid==3.2.2
asttokens==2.4.1
async-lru==2.0.4
attrs==23.2.0
Babel==2.15.0
backcall==0.2.0
bandit==1.7.9
beautifulsoup4==4.12.3
black==24.4.2
bleach==6.1.0
blinker==1.8.2
Brotli==1.1.0
bs4==0.0.2
build==1.2.1
CacheControl==0.14.0
cachelib==0.9.0
certifi==2024.6.2
cffi==1.16.0
cfgv==3.4.0
charset-normalizer==3.3.2
check-requirements-txt==1.2.2
cleo==2.1.0
click==8.1.7
clickclick==20.10.2
colorama==0.4.6
colorlog==4.8.0
comm==0.2.2
ConfigArgParse==1.7
configparser==7.0.0
ConfigUpdater==3.2
connexion==2.14.2
contextlib2==21.6.0
contourpy==1.2.1
crashtest==0.4.1
cron-descriptor==1.4.3
croniter==2.0.5
cryptography==42.0.8
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.1
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
distlib==0.3.8
Django==5.1.1
django-allauth==0.63.6
django-cors-headers==4.4.0
django-countries==7.6.1
django-crispy-forms==2.3
django-debug-toolbar==4.4.2
django-dotenv==1.4.2
django-embed-video==1.4.10
django-environ==0.11.2
django-extensions==3.2.3
django-filter==24.2
django-gulp==4.1.0
django-modelcluster==6.3
django-ninja==1.3.0
django-ninja-extra==0.21.4
django-ninja-jwt==5.3.3
django-permissionedforms==0.1
django-taggit==5.0.1
django-treebeard==4.7.1
django-widget-tweaks==1.5.0
djangorestframework==3.15.2
dnspython==2.6.1
docutils==0.21.2
draftjs-exporter==5.0.0
dulwich==0.21.7
efficientnet_pytorch==0.7.1
email_validator==2.2.0
env-tools==2.2.0
et-xmlfile==1.1.0
eval_type_backport==0.2.0
exceptiongroup==1.2.1
executing==2.0.1
Faker==26.0.0
fastapi==0.111.0
fastapi-cli==0.0.4
fastjsonschema==2.20.0
ffmpy==0.3.2
filelock==3.15.1
filetype==1.2.0
fire==0.6.0
Flask==2.2.5
Flask-AppBuilder==4.5.0
Flask-Babel==2.0.0
Flask-Caching==2.3.0
Flask-Cors==4.0.1
Flask-JWT-Extended==4.6.0
Flask-Limiter==3.7.0
Flask-Login==0.6.3
Flask-Session==0.5.0
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.2.1
fonttools==4.53.0
fqdn==1.5.1
frozenlist==1.4.1
fsspec==2024.6.0
gevent==24.2.1
geventhttpclient==2.3.1
ghp-import==2.1.0
gitdb==4.0.11
GitPython==3.1.43
google-re2==1.1.20240601
googleapis-common-protos==1.63.2
googlemaps==4.10.0
gradio_client==0.12.0
greenlet==3.0.3
grpcio==1.64.1
gunicorn==22.0.0
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
hugging-py-face==0.5.3
huggingface-hub==0.25.1
identify==2.5.36
idna==3.7
iglovikov-helper-functions==0.0.53
imagecorruptions==1.1.2
imageio==2.35.1
importlib_metadata==7.1.0
importlib_resources==6.4.0
inflection==0.5.1
iniconfig==2.0.0
injector==0.22.0
installer==0.7.0
ipykernel==6.29.4
ipython==8.25.0
ipywidgets==8.1.3
isoduration==20.11.0
isort==5.13.2
itsdangerous==2.2.0
jaraco.classes==3.4.0
jedi==0.19.1
Jinja2==3.1.4
jmespath==1.0.1
joblib==1.4.2
jpeg4py==0.1.4
json5==0.9.25
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter==1.0.0
jupyter-console==6.6.3
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.2
jupyter_core==5.7.2
jupyter_server==2.14.1
jupyter_server_terminals==0.5.3
jupyterlab==4.2.2
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.2
jupyterlab_widgets==3.0.11
kafka-python==2.0.2
keyring==24.3.1
kiwisolver==1.4.5
l18n==2021.3
laces==0.1.1
langchain==0.2.10
langchain-community==0.2.9
langchain-core==0.2.22
langchain-text-splitters==0.2.2
langsmith==0.1.90
lazy-object-proxy==1.10.0
lazy_loader==0.4
lightning-utilities==0.11.7
limits==3.13.0
linkify-it-py==2.0.3
lockfile==0.12.2
locust==2.29.1
loguru==0.7.2
Mako==1.3.5
Markdown==3.6
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.3
marshmallow-oneofschema==3.1.1
marshmallow-sqlalchemy==0.28.2
matplotlib==3.9.0
matplotlib-inline==0.1.7
mccabe==0.7.0
mdit-py-plugins==0.4.1
mdurl==0.1.2
mergedeep==1.3.4
methodtools==0.4.7
mistune==3.0.2
mkdocs==1.6.0
mkdocs-get-deps==0.2.0
more-itertools==10.3.0
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
munch==4.0.0
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
nodeenv==1.9.1
notebook==7.2.1
notebook_shim==0.2.4
numpy==1.26.4
oauthlib==3.2.2
opencv-python==4.10.0.84
opencv-python-headless==4.10.0.84
openpyxl==3.1.4
opentelemetry-api==1.25.0
opentelemetry-exporter-otlp==1.25.0
opentelemetry-exporter-otlp-proto-common==1.25.0
opentelemetry-exporter-otlp-proto-grpc==1.25.0
opentelemetry-exporter-otlp-proto-http==1.25.0
opentelemetry-proto==1.25.0
opentelemetry-sdk==1.25.0
opentelemetry-semantic-conventions==0.46b0
ordered-set==4.1.0
orjson==3.10.5
outcome==1.3.0.post0
overrides==7.7.0
packaging==23.2
pandas==2.2.2
pandocfilters==1.5.1
parso==0.8.4
pathspec==0.12.1
pbr==6.0.0
pdoc3==0.10.0
pendulum==3.0.0
people-segmentation==0.0.4
pexpect==4.9.0
pickleshare==0.7.5
pillow==10.3.0
pillow_heif==0.18.0
pipenv==2024.0.1
pkginfo==1.11.1
platformdirs==4.2.2
pluggy==1.5.0
poetry==1.8.3
poetry-core==1.9.0
poetry-plugin-export==1.8.0
pre-commit==3.7.1
pretrainedmodels==0.7.4
prison==0.2.1
prometheus_client==0.20.0
prompt_toolkit==3.0.47
protobuf==4.25.3
psutil==5.9.8
psycopg2==2.9.9
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.22
pydantic==2.9.2
pydantic-extra-types==2.8.2
pydantic-settings==2.3.3
pydantic_core==2.23.4
pydub==0.25.1
Pygments==2.18.0
PyJWT==2.8.0
pylint==3.2.3
pylint-celery==0.3
pylint-django==2.5.5
pylint-plugin-utils==0.8.2
pyOpenSSL==24.1.0
pyparsing==3.1.2
pyproject_hooks==1.1.0
PySocks==1.7.1
pytest==8.2.2
pytest-django==4.8.0
pytest-mock==3.14.0
python-daemon==3.0.1
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.9
python-nvd3==0.16.0
python-slugify==8.0.4
python3-openid==3.2.0
pytorch-lightning==2.4.0
pytorch-toolbelt==0.6.3
pytz==2024.1
PyYAML==6.0.1
pyyaml_env_tag==0.1
pyzmq==26.0.3
qtconsole==5.5.2
QtPy==2.4.1
rapidfuzz==3.9.3
rav==0.0.9
referencing==0.35.1
regex==2024.5.15
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.7.1
rich-argparse==1.5.2
rpds-py==0.18.1
ruff==0.5.5
safetensors==0.4.3
scikit-image==0.24.0
scikit-learn==1.5.2
scipy==1.14.1
segmentation-models-pytorch==0.3.4
selenium==4.24.0
selenium-page-factory==2.7
semantic-version==2.10.0
Send2Trash==1.8.3
setproctitle==1.3.3
setuptools==70.0.0
shellingham==1.5.4
simplejson==3.19.2
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
snowflake-connector-python==3.11.0
sort-requirements==1.3.0
sortedcontainers==2.4.0
soupsieve==2.5
SQLAlchemy==1.4.52
SQLAlchemy-JSONField==1.0.2
SQLAlchemy-Utils==0.41.2
sqlparse==0.5.1
stack-data==0.6.3
starlette==0.37.2
stevedore==5.2.0
sympy==1.12.1
tabulate==0.9.0
telepath==0.3.1
tenacity==8.4.2
termcolor==2.4.0
terminado==0.18.1
text-unidecode==1.3
threadpoolctl==3.5.0
tifffile==2024.9.20
time-machine==2.14.2
timm==0.9.7
tini==3.0.1
tinycss2==1.3.0
tokenizers==0.15.2
tomli==2.0.1
tomlkit==0.12.5
toolz==0.12.1
torch==2.2.2
torchmetrics==1.4.2
torchvision==0.17.2
tornado==6.4.1
tqdm==4.66.4
traitlets==5.14.3
transformers==4.38.2
trio==0.26.0
trio-websocket==0.11.1
trove-classifiers==2024.5.22
typer==0.12.3
types-python-dateutil==2.9.0.20240316
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
uc-micro-py==1.0.3
ujson==5.10.0
unicodecsv==0.14.1
universal_pathlib==0.2.2
uri-template==1.3.0
urllib3==2.2.2
uvicorn==0.30.1
uvloop==0.19.0
virtualenv==20.26.2
virtualenv-clone==0.5.7
wagtail==6.1.3
watchdog==4.0.1
watchfiles==0.22.0
wcwidth==0.2.13
webcolors==24.6.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==3.0.3
wheel==0.43.0
whitenoise==6.7.0
widgetsnbextension==4.0.11
Willow==1.8.0
wirerope==0.4.7
wrapt==1.16.0
wsproto==1.2.0
WTForms==3.1.2
xattr==1.1.0
yarl==1.9.4
zipp==3.19.2
zope.event==5.0
zope.interface==6.4.post2
