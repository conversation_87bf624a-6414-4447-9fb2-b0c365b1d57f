from fastapi import FastAPI, HTTPException
import requests
from typing import List, Dict
import os
from loguru import logger
from dotenv import load_dotenv
from googlemaps import Client as GoogleMapsClient
from uuid import uuid4

load_dotenv()
app = FastAPI()
GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAP_API_KEY")


# app = FastAPI()
gmaps = GoogleMapsClient(key=GOOGLE_MAPS_API_KEY)

shared_routes: Dict[str, Dict] = {}
# TODO:   Sort results by LONGITUDE, then LATITUDE - try optimization


# Helper function to get coordinates for a postcode
def get_coordinates(postcode: str):
    raw = f"https://maps.googleapis.com/maps/api/directions/json?origin=Disneyland&destination=Universal+Studios+Hollywood&key={GOOGLE_MAPS_API_KEY}"
    url = f"https://maps.googleapis.com/maps/api/geocode/json?address={postcode}&key={GOOGLE_MAPS_API_KEY}"
    response = requests.get(url)
    if response.status_code == 200:
        data = response.json()
        if data["results"]:
            location = data["results"][0]["geometry"]["location"]
            return (location["lat"], location["lng"])
    raise HTTPException(status_code=400, detail="Invalid postcode or API limit reached.")

# Helper function to calculate the shortest route using Google Directions API
def get_shortest_route(postcodes: List[str]):
    # Convert postcodes to coordinates
    coordinates = [get_coordinates(postcode) for postcode in postcodes]

    # Format the waypoints for the Directions API (avoid starting and ending location)
    waypoints = "|".join([f"{lat},{lng}" for lat, lng in coordinates[1:-1]])
    origin = f"{coordinates[0][0]},{coordinates[0][1]}"
    destination = f"{coordinates[-1][0]},{coordinates[-1][1]}"

    directions_url = (
        f"https://maps.googleapis.com/maps/api/directions/json"
        f"?origin={origin}&destination={destination}&waypoints=optimize:true|{waypoints}&key={GOOGLE_MAPS_API_KEY}"
    )
    response = requests.get(directions_url)
    if response.status_code == 200:
        return response.json()
    raise HTTPException(status_code=400, detail="Failed to retrieve route data.")


@app.get("/")
def base():
    return {"message": "Shortest Route API!"}





@app.post("/plan-route/")
async def plan_route(postcodes: List[str]):
    locations = []
    for postcode in postcodes:
        geocode_result = gmaps.geocode(postcode)
        if not geocode_result:
            raise HTTPException(status_code=404, detail=f"Postcode {postcode} not found")
        lat_lng = geocode_result[0]['geometry']['location']
        locations.append({'postcode': postcode, 'lat': lat_lng['lat'], 'lng': lat_lng['lng']})

    sorted_locations = sorted(locations, key=lambda loc: loc['lng'])
    waypoints = [f"{loc['lat']},{loc['lng']}" for loc in sorted_locations]

    route_response = gmaps.directions(
        origin=waypoints[0],
        destination=waypoints[-1],
        waypoints=waypoints[1:-1],
        optimize_waypoints=True,
        mode="driving"
    )

    if not route_response:
        raise HTTPException(status_code=500, detail="Failed to calculate route")

    # Calculate total journey time by summing durations for each leg
    total_duration_seconds = sum(
        leg['duration']['value'] for leg in route_response[0]['legs']
    )
    total_duration_hours = total_duration_seconds / 3600  # convert seconds to hours

    optimal_route = [
        {"location": step['start_location'], "instructions": step['html_instructions']}
        for step in route_response[0]['legs'][0]['steps']
    ]

    route_id = str(uuid4())
    shared_routes[route_id] = {
        "sorted_postcodes": [loc['postcode'] for loc in sorted_locations],
        "optimal_route": optimal_route,
        "total_duration_hours": total_duration_hours
    }

    return {
        "sorted_postcodes": [loc['postcode'] for loc in sorted_locations],
        "optimal_route": optimal_route,
        "total_duration_hours": total_duration_hours,
        "shareable_link": f"/shared-route/{route_id}"
    }

@app.get("/shared-route/{route_id}")
async def get_shared_route(route_id: str):
    route = shared_routes.get(route_id)
    if not route:
        raise HTTPException(status_code=404, detail="Route not found")
    return route


@app.post("/shortest-route/")
def calculate_shortest_route(postcodes: List[str]):
    if len(postcodes) < 2:
        raise HTTPException(status_code=400, detail="At least two postcodes are required.")
    route_data = get_shortest_route(postcodes)

    return route_data

'''




# Define FastAPI endpoint
@app.post("/shortest-route/")
def calculate_shortest_route(postcodes: List[str]):
    if len(postcodes) < 2:
        raise HTTPException(status_code=400, detail="At least two postcodes are required.")
    route_data = get_shortest_route(postcodes)

    return route_data


# Initialize Google Maps client
gmaps = GoogleMapsClient(key=GOOGLE_MAPS_API_KEY)

# In-memory store for routes (replace with a database in production)
shared_routes: Dict[str, Dict] = {}

@app.post("/plan-route/")
async def plan_route(postcodes: List[str]):
    locations = []
    for postcode in postcodes:
        geocode_result = gmaps.geocode(postcode)
        if not geocode_result:
            raise HTTPException(status_code=404, detail=f"Postcode {postcode} not found")
        lat_lng = geocode_result[0]['geometry']['location']
        locations.append({'postcode': postcode, 'lat': lat_lng['lat'], 'lng': lat_lng['lng']})

    sorted_locations = sorted(locations, key=lambda loc: loc['lng'])
    waypoints = [f"{loc['lat']},{loc['lng']}" for loc in sorted_locations]

    route_response = gmaps.directions(
        origin=waypoints[0],
        destination=waypoints[-1],
        waypoints=waypoints[1:-1],
        optimize_waypoints=True,
        mode="driving"
    )

    if not route_response:
        raise HTTPException(status_code=500, detail="Failed to calculate route")

    optimal_route = [
        {"location": step['start_location'], "instructions": step['html_instructions']}
        for step in route_response[0]['legs'][0]['steps']
    ]

    # Generate a unique ID for sharing
    route_id = str(uuid4())
    shared_routes[route_id] = {
        "sorted_postcodes": [loc['postcode'] for loc in sorted_locations],
        "optimal_route": optimal_route
    }

    return {
        "sorted_postcodes": [loc['postcode'] for loc in sorted_locations],
        "optimal_route": optimal_route,
        "shareable_link": f"/shared-route/{route_id}"
    }

@app.get("/shared-route/{route_id}")
async def get_shared_route(route_id: str):
    route = shared_routes.get(route_id)
    if not route:
        raise HTTPException(status_code=404, detail="Route not found")
    return route



@app.post("/plan-route/")
async def plan_route(postcodes: List[str]):
    # Step 1: Get Geolocation Data
    locations = []
    for postcode in postcodes:
        geocode_result = gmaps.geocode(postcode)
        if not geocode_result:
            raise HTTPException(status_code=404, detail=f"Postcode {postcode} not found")
        lat_lng = geocode_result[0]['geometry']['location']
        locations.append({'postcode': postcode, 'lat': lat_lng['lat'], 'lng': lat_lng['lng']})

    # Step 2: Sort by Longitude
    sorted_locations = sorted(locations, key=lambda loc: loc['lng'])

    # Step 3: Prepare for Routes API
    waypoints = [f"{loc['lat']},{loc['lng']}" for loc in sorted_locations]

    # Step 4: Use Routes API to get the shortest route
    route_response = gmaps.directions(
        origin=waypoints[0],
        destination=waypoints[-1],
        waypoints=waypoints[1:-1],
        optimize_waypoints=True,
        mode="driving"
    )

    if not route_response:
        raise HTTPException(status_code=500, detail="Failed to calculate route")

    # Step 5: Extract the optimal route details
    optimal_route = [
        {"location": step['start_location'], "instructions": step['html_instructions']}
        for step in route_response[0]['legs'][0]['steps']
    ]

    return {"sorted_postcodes": [loc['postcode'] for loc in sorted_locations], "optimal_route": optimal_route}

'''
